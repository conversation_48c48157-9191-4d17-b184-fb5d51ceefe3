"""
Notification tasks.

This module defines Celery tasks for sending notifications
through various channels.
"""

import logging
import os
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from celery import shared_task
from celery.exceptions import Retry

# Configure logging
logger = logging.getLogger(__name__)

# Retry configuration
MAX_RETRIES = int(os.getenv("NOTIFICATION_MAX_RETRIES", "3"))
RETRY_DELAY = int(os.getenv("NOTIFICATION_RETRY_DELAY", "5"))  # seconds

@shared_task(bind=True, 
             name="jobs.tasks.notification_tasks.send_email_notification",
             max_retries=MAX_RETRIES,
             default_retry_delay=RETRY_DELAY)
def send_email_notification(self, 
                           recipient_email: str,
                           subject: str,
                           message: str,
                           tenant_id: str,
                           user_id: Optional[str] = None,
                           template_id: Optional[str] = None,
                           template_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Send an email notification asynchronously.
    
    Args:
        self: Task instance (injected by Celery)
        recipient_email: Email address of the recipient
        subject: Email subject
        message: Email message body
        tenant_id: Tenant identifier
        user_id: Optional user identifier
        template_id: Optional email template identifier
        template_data: Optional template data for rendering
        
    Returns:
        Dict[str, Any]: Notification result
    """
    try:
        logger.info(f"Sending email notification to {recipient_email} for tenant {tenant_id}")
        
        # Record start time
        start_time = time.time()
        
        # TODO: Implement email sending logic
        # This would typically involve:
        # 1. Rendering the email template if provided
        # 2. Sending the email through an email service
        # 3. Recording the email delivery status
        
        # Simulate processing time
        time.sleep(1)
        
        # Record completion time
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Return result
        result = {
            "recipient": recipient_email,
            "tenant_id": tenant_id,
            "status": "sent",
            "processing_time": processing_time,
            "sent_at": datetime.now().isoformat(),
            # Add more result data here
        }
        
        logger.info(f"Email notification to {recipient_email} sent successfully in {processing_time:.2f} seconds")
        return result
        
    except Exception as e:
        logger.error(f"Error sending email notification to {recipient_email}: {str(e)}")
        
        # Retry the task with exponential backoff
        retry_count = self.request.retries
        if retry_count < MAX_RETRIES:
            retry_delay = RETRY_DELAY * (2 ** retry_count)  # Exponential backoff
            logger.info(f"Retrying task in {retry_delay} seconds (attempt {retry_count + 1}/{MAX_RETRIES})")
            raise self.retry(exc=e, countdown=retry_delay, max_retries=MAX_RETRIES)
        
        # If we've exhausted retries, return error result
        return {
            "recipient": recipient_email,
            "tenant_id": tenant_id,
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat(),
        }


@shared_task(bind=True,
             name="jobs.tasks.notification_tasks.send_deadline_alert",
             max_retries=MAX_RETRIES,
             default_retry_delay=RETRY_DELAY)
def send_deadline_alert(self,
                       tenant_id: str,
                       deadline_data: Dict[str, Any],
                       alert_type: str = 'critical_deadline') -> Dict[str, Any]:
    """
    Send deadline alert notifications to relevant users.

    Args:
        self: Task instance (injected by Celery)
        tenant_id: Tenant identifier
        deadline_data: Deadline information and recommendations
        alert_type: Type of alert (critical_deadline, conflict, etc.)

    Returns:
        Dict[str, Any]: Notification result
    """
    try:
        logger.info(f"Sending deadline alert for tenant {tenant_id}, type: {alert_type}")

        start_time = time.time()

        # Get tenant users who should receive deadline alerts
        from backend.db.supabase_client import get_supabase_client
        supabase = get_supabase_client(use_service_role=True)

        # Get users with deadline notification preferences enabled
        users_response = supabase.schema('tenants').from_('users').select(
            'id, email, notification_preferences'
        ).eq('tenant_id', tenant_id).eq('status', 'active').execute()

        if not users_response.data:
            logger.warning(f"No active users found for tenant {tenant_id}")
            return {"status": "no_recipients", "tenant_id": tenant_id}

        notifications_sent = 0
        errors = []

        for user in users_response.data:
            try:
                # Check user's notification preferences
                prefs = user.get('notification_preferences', {})
                if not prefs.get('deadline_alerts', True):
                    continue

                # Create notification message
                message = format_deadline_alert_message(deadline_data, alert_type)
                subject = f"Critical Deadline Alert - {deadline_data.get('title', 'Deadline Update')}"

                # Send in-app notification
                create_in_app_notification_sync(
                    user_id=user['id'],
                    tenant_id=tenant_id,
                    title=subject,
                    message=message,
                    notification_type='deadline_alert',
                    data=deadline_data
                )

                # Send email if enabled
                if prefs.get('email_alerts', True):
                    send_email_notification.delay(
                        recipient_email=user['email'],
                        subject=subject,
                        message=message,
                        tenant_id=tenant_id,
                        user_id=user['id']
                    )

                notifications_sent += 1

            except Exception as user_error:
                error_msg = f"Error sending alert to user {user['id']}: {str(user_error)}"
                logger.error(error_msg)
                errors.append(error_msg)

        end_time = time.time()
        processing_time = end_time - start_time

        result = {
            "tenant_id": tenant_id,
            "alert_type": alert_type,
            "notifications_sent": notifications_sent,
            "errors": errors,
            "status": "completed" if not errors else "completed_with_errors",
            "processing_time": processing_time,
            "sent_at": datetime.now().isoformat()
        }

        logger.info(f"Deadline alert sent to {notifications_sent} users for tenant {tenant_id}")
        return result

    except Exception as e:
        logger.error(f"Error sending deadline alert for tenant {tenant_id}: {str(e)}")

        if self.request.retries < MAX_RETRIES:
            retry_delay = RETRY_DELAY * (2 ** self.request.retries)
            logger.info(f"Retrying deadline alert in {retry_delay} seconds")
            raise self.retry(exc=e, countdown=retry_delay)

        return {
            "tenant_id": tenant_id,
            "alert_type": alert_type,
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        }


@shared_task(bind=True,
             name="jobs.tasks.notification_tasks.send_user_return_alert",
             max_retries=MAX_RETRIES,
             default_retry_delay=RETRY_DELAY)
def send_user_return_alert(self,
                          user_id: str,
                          tenant_id: str,
                          insights: Dict[str, Any],
                          hours_away: int) -> Dict[str, Any]:
    """
    Send personalized alert when user returns after being away.

    Args:
        self: Task instance (injected by Celery)
        user_id: User identifier
        tenant_id: Tenant identifier
        insights: Generated insights data
        hours_away: Number of hours user was away

    Returns:
        Dict[str, Any]: Notification result
    """
    try:
        logger.info(f"Sending return alert to user {user_id} (away {hours_away} hours)")

        # Get user information
        from backend.db.supabase_client import get_supabase_client
        supabase = get_supabase_client(use_service_role=True)

        user_response = supabase.schema('tenants').from_('users').select(
            'email, first_name, notification_preferences'
        ).eq('id', user_id).eq('tenant_id', tenant_id).single().execute()

        if not user_response.data:
            return {"status": "user_not_found", "user_id": user_id}

        user_data = user_response.data
        prefs = user_data.get('notification_preferences', {})

        # Check if user wants return notifications
        if not prefs.get('return_alerts', True):
            return {"status": "disabled_by_user", "user_id": user_id}

        # Create personalized message
        first_name = user_data.get('first_name', 'there')
        message = format_user_return_message(first_name, hours_away, insights)
        subject = f"Welcome back! Here's what's new with your deadlines"

        # Send in-app notification
        create_in_app_notification_sync(
            user_id=user_id,
            tenant_id=tenant_id,
            title=subject,
            message=message,
            notification_type='user_return',
            data=insights
        )

        result = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "hours_away": hours_away,
            "status": "sent",
            "sent_at": datetime.now().isoformat()
        }

        logger.info(f"Return alert sent to user {user_id}")
        return result

    except Exception as e:
        logger.error(f"Error sending return alert to user {user_id}: {str(e)}")

        if self.request.retries < MAX_RETRIES:
            retry_delay = RETRY_DELAY * (2 ** self.request.retries)
            raise self.retry(exc=e, countdown=retry_delay)

        return {
            "user_id": user_id,
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        }


def format_deadline_alert_message(deadline_data: Dict[str, Any], alert_type: str) -> str:
    """Format deadline alert message for notifications."""
    if alert_type == 'critical_deadline':
        return f"""
🚨 Critical Deadline Alert

{deadline_data.get('title', 'Important Deadline')}

Due: {deadline_data.get('due_date', 'Soon')}
Priority: {deadline_data.get('priority', 'High')}

{deadline_data.get('description', 'Please review this deadline immediately.')}

Action Required: {deadline_data.get('action_required', 'Review and take appropriate action')}
        """.strip()

    return f"Deadline update: {deadline_data.get('title', 'Deadline notification')}"


def format_user_return_message(first_name: str, hours_away: int, insights: Dict[str, Any]) -> str:
    """Format user return message with personalized insights."""
    recommendations = insights.get('recommendations', [])
    critical_count = len([r for r in recommendations if r.get('priority') == 'CRITICAL'])

    message = f"Welcome back, {first_name}! "

    if hours_away >= 24:
        message += f"You've been away for {hours_away // 24} day(s). "
    else:
        message += f"You've been away for {hours_away} hour(s). "

    if critical_count > 0:
        message += f"You have {critical_count} critical deadline(s) that need attention."
    else:
        message += "Your deadlines are on track."

    return message


def create_in_app_notification_sync(user_id: str, tenant_id: str, title: str,
                                   message: str, notification_type: str,
                                   data: Dict[str, Any]) -> None:
    """Create an in-app notification (sync version for Celery tasks)."""
    try:
        from backend.db.supabase_client import get_supabase_client
        supabase = get_supabase_client()

        notification_data = {
            'tenant_id': tenant_id,
            'user_id': user_id,
            'type': notification_type,
            'message': f"{title}: {message}",
            'severity': 'high' if 'critical' in notification_type.lower() else 'medium',
            'context': data,
            'created_at': datetime.now().isoformat()
        }

        supabase.schema('tenants').from_('notifications').insert(notification_data).execute()
        logger.info(f"Created in-app notification for user {user_id}")

    except Exception as e:
        logger.error(f"Error creating in-app notification: {e}")


async def create_in_app_notification(user_id: str, tenant_id: str, title: str,
                                   message: str, notification_type: str,
                                   data: Dict[str, Any]) -> None:
    """Create an in-app notification (async version)."""
    try:
        from backend.db.supabase_client import get_supabase_client
        supabase = get_supabase_client(use_service_role=True)

        notification_data = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'type': notification_type,
            'message': title,
            'read': False,
            'severity': 'high' if 'critical' in notification_type.lower() else 'medium',
            'context': data,
            'created_at': datetime.now().isoformat()
        }

        supabase.schema('tenants').from_('notifications').insert(notification_data).execute()
        logger.info(f"Created in-app notification for user {user_id}")

    except Exception as e:
        logger.error(f"Error creating in-app notification: {e}")
