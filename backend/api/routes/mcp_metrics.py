"""
MCP Rules Engine Metrics API

Lightweight metrics endpoint for D2 staging deployment monitoring.
Provides real-time metrics from Google Cloud Logging for MCP Rules Engine performance.
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict

from fastapi import APIRouter, Depends

try:
    from google.cloud import logging as cloud_logging
    from google.cloud import monitoring_v3
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    cloud_logging = None
    monitoring_v3 = None
    GOOGLE_CLOUD_AVAILABLE = False

from backend.api.dependencies.auth import require_super_admin
from backend.api.dependencies.authorization import require_metrics_access

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/internal", tags=["metrics"])

# Initialize Google Cloud clients
if GOOGLE_CLOUD_AVAILABLE:
    try:
        monitoring_client = monitoring_v3.MetricServiceClient()
        logging_client = cloud_logging.Client()
        GCP_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', os.getenv('GCP_PROJECT'))

        if not GCP_PROJECT:
            logger.warning("GCP_PROJECT not set - metrics will return mock data")

    except Exception as e:
        logger.warning(f"Failed to initialize Google Cloud clients: {e}")
        monitoring_client = None
        logging_client = None
        GCP_PROJECT = None
else:
    logger.warning("Google Cloud packages not available - metrics will return mock data")
    monitoring_client = None
    logging_client = None
    GCP_PROJECT = None


@router.get("/mcp-metrics")
async def get_mcp_metrics(
    _super_admin: dict = Depends(require_super_admin),
    _metrics_access: dict = Depends(require_metrics_access)
) -> Dict[str, Any]:
    """
    Get real-time MCP Rules Engine metrics from Google Cloud Logging.
    
    Returns:
        - totalRequests: Number of MCP API calls in last 10 minutes
        - successRate: Percentage of successful requests
        - avgLatency: Average response time in milliseconds
        - errorRate: Percentage of failed requests
        - lastUpdated: Timestamp of last metric update
    """
    
    if not monitoring_client or not logging_client or not GCP_PROJECT:
        # Return mock data if Cloud clients not available
        logger.info("Returning mock metrics - Google Cloud clients not available")
        return _get_mock_metrics()
    
    try:
        # Time range: last 10 minutes
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(minutes=10)
        
        # Query logs for MCP requests
        filter_str = (
            'resource.type="generic_node" '
            'AND labels.service="mcp-rules-engine" '
            f'AND timestamp>="{start_time.isoformat()}Z" '
            f'AND timestamp<="{end_time.isoformat()}Z"'
        )
        
        entries = list(
            logging_client.list_entries(filter_=filter_str, max_results=1000)
        )
        
        if not entries:
            logger.info("No MCP log entries found - returning zero metrics")
            return {
                "totalRequests": 0,
                "successRate": 0.0,
                "avgLatency": 0,
                "errorRate": 0.0,
                "lastUpdated": datetime.utcnow().isoformat(),
                "dataSource": "google_cloud_logging",
                "timeRange": "10_minutes"
            }
        
        # Process log entries
        total_requests = len(entries)
        successful_requests = 0
        latencies = []
        
        for entry in entries:
            # Check success status
            if entry.labels.get('success') == 'true':
                successful_requests += 1
            
            # Extract latency from jsonPayload
            if hasattr(entry, 'payload') and isinstance(entry.payload, dict):
                latency = entry.payload.get('latency_ms')
                if latency is not None:
                    try:
                        latencies.append(float(latency))
                    except (ValueError, TypeError):
                        pass
        
        # Calculate metrics
        success_rate = (
            (successful_requests / total_requests * 100)
            if total_requests > 0 else 0
        )
        error_rate = (
            ((total_requests - successful_requests) / total_requests * 100)
            if total_requests > 0 else 0
        )
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        
        logger.info(
            f"MCP metrics calculated: {total_requests} requests, "
            f"{success_rate:.1f}% success, {avg_latency:.0f}ms avg latency"
        )
        
        return {
            "totalRequests": total_requests,
            "successRate": round(success_rate, 1),
            "avgLatency": round(avg_latency),
            "errorRate": round(error_rate, 1),
            "lastUpdated": datetime.utcnow().isoformat(),
            "dataSource": "google_cloud_logging",
            "timeRange": "10_minutes"
        }
        
    except Exception as e:
        logger.error(f"Error fetching MCP metrics: {e}")
        # Fallback to mock data on error
        return _get_mock_metrics()


def _get_mock_metrics() -> Dict[str, Any]:
    """
    Return mock metrics for development/testing.
    These are realistic values for D2 staging validation.
    """
    return {
        "totalRequests": 47,  # Realistic staging volume
        "successRate": 97.9,  # Good but not perfect
        "avgLatency": 285,    # Well under 700ms target
        "errorRate": 2.1,     # Slightly above 1% target (realistic for staging)
        "lastUpdated": datetime.utcnow().isoformat(),
        "dataSource": "mock_data",
        "timeRange": "10_minutes"
    }


@router.get("/mcp-metrics/health")
async def get_metrics_health() -> Dict[str, Any]:
    """
    Health check for metrics collection system.
    """
    return {
        "status": "healthy" if (monitoring_client and logging_client) else "degraded",
        "googleCloudAvailable": monitoring_client is not None,
        "loggingClientAvailable": logging_client is not None,
        "gcpProject": GCP_PROJECT,
        "timestamp": datetime.utcnow().isoformat()
    }
