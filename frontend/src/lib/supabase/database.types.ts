export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      cases: {
        Row: {
          id: string
          case_name: string
          case_name_full: string | null
          court_id: string | null
          jurisdiction: string
          date_filed: string | null
          status: string | null
          docket_number: string | null
          nature: string | null
          citation: string[] | null
          precedential: boolean | null
          source: string | null
          source_id: string | null
          cluster_id: string | null
          docket_id: string | null
          gcs_path: string | null
          pinecone_id: string | null
          opinion_count: number | null
          citation_count: number | null
          completeness_score: number | null
          document_quality: string | null
          metadata_quality: string | null
          user_id: string | null
          user_role: string | null
          tenant_id: string | null
          created_at: string | null
          updated_at: string | null
          practice_areas: string[] | null
          primary_practice_area: string | null
          cross_practice_references: number | null
        }
        Insert: {
          id: string
          case_name: string
          case_name_full?: string | null
          court_id?: string | null
          jurisdiction: string
          date_filed?: string | null
          status?: string | null
          docket_number?: string | null
          nature?: string | null
          citation?: string[] | null
          precedential?: boolean | null
          source?: string | null
          source_id?: string | null
          cluster_id?: string | null
          docket_id?: string | null
          gcs_path?: string | null
          pinecone_id?: string | null
          opinion_count?: number | null
          citation_count?: number | null
          completeness_score?: number | null
          document_quality?: string | null
          metadata_quality?: string | null
          user_id?: string | null
          user_role?: string | null
          tenant_id?: string | null
          created_at?: string | null
          updated_at?: string | null
          practice_areas?: string[] | null
          primary_practice_area?: string | null
          cross_practice_references?: number | null
        }
        Update: {
          id?: string
          case_name?: string
          case_name_full?: string | null
          court_id?: string | null
          jurisdiction?: string
          date_filed?: string | null
          status?: string | null
          docket_number?: string | null
          nature?: string | null
          citation?: string[] | null
          precedential?: boolean | null
          source?: string | null
          source_id?: string | null
          cluster_id?: string | null
          docket_id?: string | null
          gcs_path?: string | null
          pinecone_id?: string | null
          opinion_count?: number | null
          citation_count?: number | null
          completeness_score?: number | null
          document_quality?: string | null
          metadata_quality?: string | null
          user_id?: string | null
          user_role?: string | null
          tenant_id?: string | null
          created_at?: string | null
          updated_at?: string | null
          practice_areas?: string[] | null
          primary_practice_area?: string | null
          cross_practice_references?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  tenants: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: string
          tenant_id: string
          created_at: string | null
          last_login: string | null
          settings: Json | null
          firm_role: string | null
          auth_user_id: string | null
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          updated_at: string | null
          phone: string | null
          phone_verified: boolean | null
          last_activity_at: string | null
        }
        Insert: {
          id?: string
          email: string
          role: string
          tenant_id: string
          created_at?: string | null
          last_login?: string | null
          settings?: Json | null
          firm_role?: string | null
          auth_user_id?: string | null
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          updated_at?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          last_activity_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          role?: string
          tenant_id?: string
          created_at?: string | null
          last_login?: string | null
          settings?: Json | null
          firm_role?: string | null
          auth_user_id?: string | null
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          updated_at?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          last_activity_at?: string | null
        }
        Relationships: []
      }
      clients: {
        Row: {
          id: string
          tenant_id: string
          first_name: string
          last_name: string
          middle_name: string | null
          email: string
          phone: string | null
          phone_primary: string | null
          address: Json | null
          created_at: string
          updated_at: string | null
          metadata: Json | null
          date_of_birth: string | null
          assigned_attorney_id: string | null
          status: string | null
          notes: string | null
          created_by: string | null
          client_type: string | null
          intake_date: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          first_name: string
          last_name: string
          middle_name?: string | null
          email: string
          phone?: string | null
          phone_primary?: string | null
          address?: Json | null
          created_at?: string
          updated_at?: string | null
          metadata?: Json | null
          date_of_birth?: string | null
          assigned_attorney_id?: string | null
          status?: string | null
          notes?: string | null
          created_by?: string | null
          client_type?: string | null
          intake_date?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          first_name?: string
          last_name?: string
          middle_name?: string | null
          email?: string
          phone?: string | null
          phone_primary?: string | null
          address?: Json | null
          created_at?: string
          updated_at?: string | null
          metadata?: Json | null
          date_of_birth?: string | null
          assigned_attorney_id?: string | null
          status?: string | null
          notes?: string | null
          created_by?: string | null
          client_type?: string | null
          intake_date?: string | null
        }
        Relationships: []
      }

      matters: {
        Row: {
          id: string
          tenant_id: string
          title: string
          description: string | null
          status: string
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          metadata: Json | null
          practice_area: string
          sensitive: boolean | null
          opened_date: string | null
          closed_date: string | null
          date_of_incident: string | null
          consulted_attorney: boolean | null
          previously_consulted: boolean | null
          intake_priority: string | null
          display_label: string | null
          trial_date: string | null
          priority_level: string | null
          work_type: string
          rejection_reason: string | null
          client_id: string
          case_type: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          title: string
          description?: string | null
          status: string
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          metadata?: Json | null
          practice_area: string
          sensitive?: boolean | null
          opened_date?: string | null
          closed_date?: string | null
          date_of_incident?: string | null
          consulted_attorney?: boolean | null
          previously_consulted?: boolean | null
          intake_priority?: string | null
          display_label?: string | null
          trial_date?: string | null
          priority_level?: string | null
          work_type?: string
          rejection_reason?: string | null
          client_id: string
          case_type?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          title?: string
          description?: string | null
          status?: string
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          metadata?: Json | null
          practice_area?: string
          sensitive?: boolean | null
          opened_date?: string | null
          closed_date?: string | null
          date_of_incident?: string | null
          consulted_attorney?: boolean | null
          previously_consulted?: boolean | null
          intake_priority?: string | null
          display_label?: string | null
          trial_date?: string | null
          priority_level?: string | null
          work_type?: string
          rejection_reason?: string | null
          client_id?: string
          case_type?: string | null
        }
        Relationships: []
      }
      case_clients: {
        Row: {
          tenant_id: string
          client_id: string
          matter_id: string
        }
        Insert: {
          tenant_id: string
          client_id: string
          matter_id: string
        }
        Update: {
          tenant_id?: string
          client_id?: string
          matter_id?: string
        }
        Relationships: []
      }
      task_deadlines: {
        Row: {
          id: string
          tenant_id: string
          task_id: string
          deadline_date: string
          rule_citation: string | null
          calc_steps: Json | null
          overridden: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          task_id: string
          deadline_date: string
          rule_citation?: string | null
          calc_steps?: Json | null
          overridden?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          task_id?: string
          deadline_date?: string
          rule_citation?: string | null
          calc_steps?: Json | null
          overridden?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      firms: {
        Row: {
          id: string
          tenant_id: string
          name: string
          address: string
          practice_areas: string[]
          specializations: string[]
          settings: Json
          metadata: Json
          jurisdiction_settings: Json
          created_at: string
          updated_at: string | null
          mcp_status: string | null
          mcp_secret_path: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          name: string
          address: string
          practice_areas: string[]
          specializations: string[]
          settings: Json
          metadata: Json
          jurisdiction_settings: Json
          created_at?: string
          updated_at?: string | null
          mcp_status?: string | null
          mcp_secret_path?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          name?: string
          address?: string
          practice_areas?: string[]
          specializations?: string[]
          settings?: Json
          metadata?: Json
          jurisdiction_settings?: Json
          created_at?: string
          updated_at?: string | null
          mcp_status?: string | null
          mcp_secret_path?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          id: string
          tenant_id: string
          user_id: string
          type: string
          message: string
          read: boolean | null
          severity: string | null
          context: Json | null
          sender_id: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          user_id: string
          type: string
          message: string
          read?: boolean | null
          severity?: string | null
          context?: Json | null
          sender_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          user_id?: string
          type?: string
          message?: string
          read?: boolean | null
          severity?: string | null
          context?: Json | null
          sender_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      tasks: {
        Row: {
          id: string
          tenant_id: string
          title: string
          description: string | null
          due_date: string | null
          status: string
          assigned_to: string | null
          related_case: string | null
          ai_metadata: Json | null
          created_by: string
          created_at: string | null
          updated_by: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          title: string
          description?: string | null
          due_date?: string | null
          status?: string
          assigned_to?: string | null
          related_case?: string | null
          ai_metadata?: Json | null
          created_by: string
          created_at?: string | null
          updated_by?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          title?: string
          description?: string | null
          due_date?: string | null
          status?: string
          assigned_to?: string | null
          related_case?: string | null
          ai_metadata?: Json | null
          created_by?: string
          created_at?: string | null
          updated_by?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      authored_documents: {
        Row: {
          id: string
          tenant_id: string
          client_id: string | null
          template_id: string | null
          title: string
          content: string
          variables_used: Json | null
          status: string
          version: number | null
          gcs_path: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          sent_at: string | null
          signed_at: string | null
          metadata: Json | null
          updated_by: string | null
          embedding_status: string | null
          last_embedded_at: string | null
          matter_id: string
        }
        Insert: {
          id?: string
          tenant_id: string
          client_id?: string | null
          template_id?: string | null
          title: string
          content: string
          variables_used?: Json | null
          status?: string
          version?: number | null
          gcs_path?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          sent_at?: string | null
          signed_at?: string | null
          metadata?: Json | null
          updated_by?: string | null
          embedding_status?: string | null
          last_embedded_at?: string | null
          matter_id: string
        }
        Update: {
          id?: string
          tenant_id?: string
          client_id?: string | null
          template_id?: string | null
          title?: string
          content?: string
          variables_used?: Json | null
          status?: string
          version?: number | null
          gcs_path?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          sent_at?: string | null
          signed_at?: string | null
          metadata?: Json | null
          updated_by?: string | null
          embedding_status?: string | null
          last_embedded_at?: string | null
          matter_id?: string
        }
        Relationships: []
      }
      case_documents: {
        Row: {
          id: string
          tenant_id: string
          title: string
          citation: string | null
          gcs_path: string | null
          document_type: string
          sensitive: boolean | null
          created_by: string | null
          created_at: string | null
          metadata: Json | null
          updated_by: string | null
          updated_at: string | null
          practice_area: string | null
          document_category: string | null
          subcategory: string | null
          matter_id: string
        }
        Insert: {
          id?: string
          tenant_id: string
          title: string
          citation?: string | null
          gcs_path?: string | null
          document_type: string
          sensitive?: boolean | null
          created_by?: string | null
          created_at?: string | null
          metadata?: Json | null
          updated_by?: string | null
          updated_at?: string | null
          practice_area?: string | null
          document_category?: string | null
          subcategory?: string | null
          matter_id: string
        }
        Update: {
          id?: string
          tenant_id?: string
          title?: string
          citation?: string | null
          gcs_path?: string | null
          document_type?: string
          sensitive?: boolean | null
          created_by?: string | null
          created_at?: string | null
          metadata?: Json | null
          updated_by?: string | null
          updated_at?: string | null
          practice_area?: string | null
          document_category?: string | null
          subcategory?: string | null
          matter_id?: string
        }
        Relationships: []
      }
      deadlines: {
        Row: {
          id: string
          tenant_id: string
          title: string
          description: string | null
          due_date: string
          base_date: string | null
          completed_at: string | null
          case_id: string | null
          rule_id: string | null
          assigned_to: string[] | null
          completed_by: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          priority: string | null
          status: string | null
          calculation_notes: string | null
        }
        Insert: {
          id?: string
          tenant_id: string
          title: string
          description?: string | null
          due_date: string
          base_date?: string | null
          completed_at?: string | null
          case_id?: string | null
          rule_id?: string | null
          assigned_to?: string[] | null
          completed_by?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          priority?: string | null
          status?: string | null
          calculation_notes?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string
          title?: string
          description?: string | null
          due_date?: string
          base_date?: string | null
          completed_at?: string | null
          case_id?: string | null
          rule_id?: string | null
          assigned_to?: string[] | null
          completed_by?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          priority?: string | null
          status?: string | null
          calculation_notes?: string | null
        }
        Relationships: []
      }
      legal_rules: {
        Row: {
          id: string
          tenant_id: string | null
          title: string
          description: string | null
          rule_text: string
          jurisdiction: string
          practice_area: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          tenant_id?: string | null
          title: string
          description?: string | null
          rule_text: string
          jurisdiction: string
          practice_area: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string | null
          title?: string
          description?: string | null
          rule_text?: string
          jurisdiction?: string
          practice_area?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  security: {
    Tables: {
      security_alerts: {
        Row: {
          id: string
          tenant_id: string | null
          alert_type: string
          severity: string
          message: string
          metadata: Json | null
          resolved: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          tenant_id?: string | null
          alert_type: string
          severity: string
          message: string
          metadata?: Json | null
          resolved?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string | null
          alert_type?: string
          severity?: string
          message?: string
          metadata?: Json | null
          resolved?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      security_config: {
        Row: {
          id: string
          tenant_id: string | null
          config_key: string
          config_value: Json
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          tenant_id?: string | null
          config_key: string
          config_value: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string | null
          config_key?: string
          config_value?: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      security_events: {
        Row: {
          id: string
          tenant_id: string | null
          event_type: string
          user_id: string | null
          ip_address: string | null
          user_agent: string | null
          metadata: Json | null
          created_at: string | null
        }
        Insert: {
          id?: string
          tenant_id?: string | null
          event_type: string
          user_id?: string | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Json | null
          created_at?: string | null
        }
        Update: {
          id?: string
          tenant_id?: string | null
          event_type?: string
          user_id?: string | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Json | null
          created_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}