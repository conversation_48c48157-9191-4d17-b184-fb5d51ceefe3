import { <PERSON><PERSON> } from './database.types';

/**
 * Tenant-specific database tables types
 * This file contains types for tables that exist in the tenant schema.
 */

// Resource Usage table types
export interface ResourceUsageTable {
  Row: {
    id: string
    tenant_id: string
    usage_type: string
    usage_count: number
    resource_size_bytes: number | null
    period_start: string
    period_end: string
    created_at: string
  }
  Insert: {
    id?: string
    tenant_id: string
    usage_type: string
    usage_count: number
    resource_size_bytes?: number | null
    period_start: string
    period_end: string
    created_at?: string
  }
  Update: {
    id?: string
    tenant_id?: string
    usage_type?: string
    usage_count?: number
    resource_size_bytes?: number | null
    period_start?: string
    period_end?: string
    created_at?: string
  }
}

// Tenant Quotas table types
export interface TenantQuotasTable {
  Row: {
    id: string
    tenant_id: string
    max_monthly_uploads: number
    max_concurrent_processing: number
    max_document_size_mb: number
    created_at: string
    updated_at: string | null
  }
  Insert: {
    id?: string
    tenant_id: string
    max_monthly_uploads: number
    max_concurrent_processing: number
    max_document_size_mb: number
    created_at?: string
    updated_at?: string | null
  }
  Update: {
    id?: string
    tenant_id?: string
    max_monthly_uploads?: number
    max_concurrent_processing?: number
    max_document_size_mb?: number
    created_at?: string
    updated_at?: string | null
  }
}

// Tasks table types
export interface TasksTable {
  Row: {
    id: string
    tenant_id: string
    title: string
    description: string | null
    status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
    due_date: string | null
    priority: 'low' | 'medium' | 'high'
    assigned_to: string | null
    created_by: string
    created_at: string
    updated_at: string | null
    completed_at: string | null
    case_id: string | null
    client_id: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    title: string
    description?: string | null
    status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
    due_date?: string | null
    priority?: 'low' | 'medium' | 'high'
    assigned_to?: string | null
    created_by: string
    created_at?: string
    updated_at?: string | null
    completed_at?: string | null
    case_id?: string | null
    client_id?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    title?: string
    description?: string | null
    status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
    due_date?: string | null
    priority?: 'low' | 'medium' | 'high'
    assigned_to?: string | null
    created_by?: string
    created_at?: string
    updated_at?: string | null
    completed_at?: string | null
    case_id?: string | null
    client_id?: string | null
    metadata?: Json | null
  }
}

// Task History table types
export interface TaskHistoryTable {
  Row: {
    id: string
    task_id: string
    tenant_id: string
    action: string
    previous_status: string | null
    new_status: string | null
    performed_by: string
    performed_at: string
    notes: string | null
  }
  Insert: {
    id?: string
    task_id: string
    tenant_id: string
    action: string
    previous_status?: string | null
    new_status?: string | null
    performed_by: string
    performed_at?: string
    notes?: string | null
  }
  Update: {
    id?: string
    task_id?: string
    tenant_id?: string
    action?: string
    previous_status?: string | null
    new_status?: string | null
    performed_by?: string
    performed_at?: string
    notes?: string | null
  }
}

// Cases table types
export interface CasesTable {
  Row: {
    id: string
    tenant_id: string
    title: string
    case_number: string | null
    status: string
    client_id: string | null
    assigned_to: string | null
    created_by: string
    created_at: string
    updated_at: string | null
    closed_at: string | null
    practice_area: string | null
    description: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    title: string
    case_number?: string | null
    status: string
    client_id?: string | null
    assigned_to?: string | null
    created_by: string
    created_at?: string
    updated_at?: string | null
    closed_at?: string | null
    practice_area?: string | null
    description?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    title?: string
    case_number?: string | null
    status?: string
    client_id?: string | null
    assigned_to?: string | null
    created_by?: string
    created_at?: string
    updated_at?: string | null
    closed_at?: string | null
    practice_area?: string | null
    description?: string | null
    metadata?: Json | null
  }
}

// Events table types
export interface EventsTable {
  Row: {
    id: string
    tenant_id: string
    title: string
    description: string | null
    start_time: string
    end_time: string | null
    all_day: boolean
    location: string | null
    event_type: string
    case_id: string | null
    created_by: string
    created_at: string
    updated_at: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    title: string
    description?: string | null
    start_time: string
    end_time?: string | null
    all_day?: boolean
    location?: string | null
    event_type: string
    case_id?: string | null
    created_by: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    title?: string
    description?: string | null
    start_time?: string
    end_time?: string | null
    all_day?: boolean
    location?: string | null
    event_type?: string
    case_id?: string | null
    created_by?: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
}

// Users table types (tenant-specific users)
export interface UsersTable {
  Row: {
    id: string
    tenant_id: string
    auth_id: string
    email: string
    first_name: string | null
    last_name: string | null
    role: string
    status: string
    created_at: string
    updated_at: string | null
    last_login: string | null
    last_activity_at: string | null
    profile_image_url: string | null
    settings: Json | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    auth_id: string
    email: string
    first_name?: string | null
    last_name?: string | null
    role: string
    status?: string
    created_at?: string
    updated_at?: string | null
    last_login?: string | null
    last_activity_at?: string | null
    profile_image_url?: string | null
    settings?: Json | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    auth_id?: string
    email?: string
    first_name?: string | null
    last_name?: string | null
    role?: string
    status?: string
    created_at?: string
    updated_at?: string | null
    last_login?: string | null
    last_activity_at?: string | null
    profile_image_url?: string | null
    settings?: Json | null
    metadata?: Json | null
  }
}

// Additional table types for missing tables
export interface ClientsTable {
  Row: {
    id: string
    tenant_id: string
    first_name: string
    last_name: string
    email: string
    phone: string | null
    address: Json | null
    created_at: string
    updated_at: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    first_name: string
    last_name: string
    email: string
    phone?: string | null
    address?: Json | null
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    first_name?: string
    last_name?: string
    email?: string
    phone?: string | null
    address?: Json | null
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
}

export interface CaseClientsTable {
  Row: {
    case_id: string
    client_id: string
    tenant_id: string
    created_at: string
  }
  Insert: {
    case_id: string
    client_id: string
    tenant_id: string
    created_at?: string
  }
  Update: {
    case_id?: string
    client_id?: string
    tenant_id?: string
    created_at?: string
  }
}

export interface TaskDeadlinesTable {
  Row: {
    id: string
    task_id: string
    tenant_id: string
    deadline_date: string
    status: string
    created_at: string
    updated_at: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    task_id: string
    tenant_id: string
    deadline_date: string
    status?: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    task_id?: string
    tenant_id?: string
    deadline_date?: string
    status?: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
}

export interface FirmsTable {
  Row: {
    id: string
    tenant_id: string
    name: string
    address: string
    practice_areas: string[]
    specializations: string[]
    settings: Json
    metadata: Json
    jurisdiction_settings: Json
    created_at: string
    updated_at: string | null
    mcp_status: string | null
    mcp_secret_path: string | null
  }
  Insert: {
    id?: string
    tenant_id: string
    name: string
    address: string
    practice_areas: string[]
    specializations: string[]
    settings: Json
    metadata: Json
    jurisdiction_settings: Json
    created_at?: string
    updated_at?: string | null
    mcp_status?: string | null
    mcp_secret_path?: string | null
  }
  Update: {
    id?: string
    tenant_id?: string
    name?: string
    address?: string
    practice_areas?: string[]
    specializations?: string[]
    settings?: Json
    metadata?: Json
    jurisdiction_settings?: Json
    created_at?: string
    updated_at?: string | null
    mcp_status?: string | null
    mcp_secret_path?: string | null
  }
}

export interface NotificationsTable {
  Row: {
    id: string
    tenant_id: string
    user_id: string
    title: string
    message: string
    type: string
    status: string
    created_at: string
    updated_at: string | null
    metadata: Json | null
  }
  Insert: {
    id?: string
    tenant_id: string
    user_id: string
    title: string
    message: string
    type: string
    status?: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
  Update: {
    id?: string
    tenant_id?: string
    user_id?: string
    title?: string
    message?: string
    type?: string
    status?: string
    created_at?: string
    updated_at?: string | null
    metadata?: Json | null
  }
}

// Define a type that includes all tenant-specific tables
export type TenantTables = {
  resource_usage: ResourceUsageTable
  tenant_quotas: TenantQuotasTable
  tasks: TasksTable
  task_history: TaskHistoryTable
  cases: CasesTable
  events: EventsTable
  users: UsersTable
  clients: ClientsTable
  case_clients: CaseClientsTable
  task_deadlines: TaskDeadlinesTable
  firms: FirmsTable
  notifications: NotificationsTable
}
