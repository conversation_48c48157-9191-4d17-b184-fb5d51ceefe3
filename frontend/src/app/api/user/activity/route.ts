import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse the request body
    const body = await request.json()
    const { action, last_activity_time } = body

    if (action === 'user_return' && last_activity_time) {
      // User is returning after being away
      try {
        // Get user's tenant ID
        const { data: userData, error: userError } = await supabase
          .schema('tenants')
          .from('users')
          .select('tenant_id')
          .eq('id', user.id)
          .single()

        if (userError || !userData) {
          console.error('Error fetching user data:', userError)
          return NextResponse.json({ error: 'Failed to fetch user data' }, { status: 500 })
        }

        const tenantId = userData.tenant_id

        // Calculate time away
        const lastActivity = new Date(last_activity_time)
        const now = new Date()
        const timeAwayMs = now.getTime() - lastActivity.getTime()
        const timeAwayHours = timeAwayMs / (1000 * 60 * 60)

        // Only trigger insights if user was away for more than 1 hour
        if (timeAwayHours >= 1) {
          // Trigger user return insights generation
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/api/jobs/user-return-insights`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_KEY}`
            },
            body: JSON.stringify({
              user_id: user.id,
              tenant_id: tenantId,
              last_activity_time: last_activity_time
            })
          })

          if (response.ok) {
            const jobData = await response.json()
            
            // Log the user return event
            await supabase
              .schema('security')
              .from('events')
              .insert({
                event_type: 'user.return_detected',
                event_category: 'user_activity',
                user_id: user.id,
                details: {
                  tenant_id: tenantId,
                  time_away_hours: timeAwayHours,
                  last_activity_time: last_activity_time,
                  insights_job_id: jobData.job_id,
                  timestamp: new Date().toISOString()
                },
                created_at: new Date().toISOString()
              })

            return NextResponse.json({
              success: true,
              message: 'User return insights triggered',
              job_id: jobData.job_id,
              time_away_hours: timeAwayHours
            })
          } else {
            console.error('Failed to trigger user return insights:', await response.text())
          }
        }

        // Update user's last activity time
        await supabase
          .schema('tenants')
          .from('users')
          .update({ 
            last_activity_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)

        return NextResponse.json({
          success: true,
          message: 'Activity recorded',
          time_away_hours: timeAwayHours,
          insights_triggered: timeAwayHours >= 1
        })

      } catch (error) {
        console.error('Error processing user return:', error)
        return NextResponse.json({ error: 'Failed to process user return' }, { status: 500 })
      }

    } else if (action === 'heartbeat') {
      // Regular activity heartbeat
      try {
        await supabase
          .schema('tenants')
          .from('users')
          .update({ 
            last_activity_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)

        return NextResponse.json({ success: true, message: 'Heartbeat recorded' })

      } catch (error) {
        console.error('Error recording heartbeat:', error)
        return NextResponse.json({ error: 'Failed to record heartbeat' }, { status: 500 })
      }

    } else {
      return NextResponse.json({ error: 'Invalid action or missing parameters' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in user activity POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's last activity time
    const { data: userData, error: userError } = await supabase
      .schema('tenants')
      .from('users')
      .select('last_activity_at, created_at')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user activity:', userError)
      return NextResponse.json({ error: 'Failed to fetch user activity' }, { status: 500 })
    }

    // Use last_activity_at or created_at as activity indicators
    const lastActivity = userData?.last_activity_at || userData?.created_at
    const now = new Date()
    const timeAwayMs = lastActivity ? now.getTime() - new Date(lastActivity).getTime() : 0
    const timeAwayHours = timeAwayMs / (1000 * 60 * 60)

    return NextResponse.json({
      last_activity_at: lastActivity,
      time_away_hours: timeAwayHours,
      should_trigger_insights: timeAwayHours >= 1
    })

  } catch (error) {
    console.error('Error in user activity GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
