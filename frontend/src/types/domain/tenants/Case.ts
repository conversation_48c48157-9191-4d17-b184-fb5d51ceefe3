/**
 * Case types for dynamic labeling
 *
 * This module provides type aliases for matters when they should be displayed as "cases"
 * (i.e., when work_type is 'litigation'). This supports the dynamic labeling system
 * where litigation matters are called "cases" and other matters are called "matters".
 */

// Re-export Matter types with Case aliases for dynamic labeling
export type {
  Matter as Case,
  MatterInput as CaseInput,
  MatterMetadata as CaseMetadata,
  MatterStatus as CaseStatus,
  MatterPriorityLevel as CasePriorityLevel,
  PracticeArea,
  WorkType
} from './Matter';

// Re-export utility functions with Case aliases
export {
  getMatterDisplayLabel as getCaseDisplayLabel,
  isLitigationMatter as isLitigationCase,
  getMatterStatusDisplayName as getCaseStatusDisplayName,
  getMatterStatusColor as getCaseStatusColor,
  getPracticeAreaDisplayName,
  getPracticeAreaColor,
  getPriorityDisplayName
} from './Matter';
