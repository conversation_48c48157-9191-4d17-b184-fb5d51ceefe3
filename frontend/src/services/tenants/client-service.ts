import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../../lib/supabase/database.types';
import {
  mapClientFromDb,
  mapClientsFromDb,
  mapClientToDb,
  mapClientUpdateToDb
} from '../../mappers/tenants/client';
import type { ClientRow } from '../../mappers/tenants/client';
import { Json } from '../../lib/supabase/database.types';
import type { Client, ClientInput } from '../../types/domain/tenants/Client';
import { execSafeQuery, execCountQuery, execRpcQuery } from '../../lib/supabase/utils';

// Query parameters for client service
export interface ClientQueryParams {
  page?: number;
  limit?: number;
  status?: string;
  searchTerm?: string;
}

export interface ClientQueryResult {
  clients: Client[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Service for managing clients
 * All database interactions are encapsulated here and proper tenant isolation is enforced
 */
export class ClientService {
  constructor(
    private supabase: SupabaseClient<Database>,
    private tenantId: string
  ) {
    if (!tenantId) {
      throw new Error('Tenant ID is required for ClientService');
    }
  }

  /**
   * Get all clients with optional filtering and pagination
   */
  async getAll(params: ClientQueryParams = {}): Promise<ClientQueryResult> {
    const {
      page = 1,
      limit = 20,
      status,
      searchTerm
    } = params;

    // Calculate pagination parameters
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // First, get the total count for pagination info using our helper function
    const total = await execCountQuery(() => this.supabase
      .schema('tenants')
      .from('clients')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId));

    // Build base query for the data
    let query = this.supabase
      .schema('tenants')
      .from('clients')
      .select(`
        *,
        assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)
      `)
      .eq('tenant_id', this.tenantId)
      .order('last_name', { ascending: true })
      .range(from, to);

    // Apply status filter if provided
    if (status) {
      query = query.eq('status', status);
    }

    // Apply search if provided
    if (searchTerm) {
      query = query.or(
        `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`
      );
    }

    // Execute the query with our safe query helper
    const data = await execSafeQuery<ClientRow[]>(() => query);

    if (!data) {
      console.error('Error fetching clients');
      throw new Error('Failed to fetch clients');
    }

    // Calculate pagination info
    const totalCount = total;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      clients: mapClientsFromDb(data || []),
      totalCount,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Get a client by ID
   */
  async getById(id: string): Promise<Client | null> {
    const data = await execSafeQuery<ClientRow>(() =>
      this.supabase
        .schema('tenants')
        .from('clients')
        .select(`
          *,
          assigned_attorney:assigned_attorney_id(id, email, first_name, last_name),
          cases(id, title, status)
        `)
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .single()
    );

    // If no data was returned, client doesn't exist
    if (!data) {
      return null;
    }

    return mapClientFromDb(data);
  }

  /**
   * Create a new client
   */
  async create(userId: string, input: ClientInput): Promise<Client> {
    const clientData = mapClientToDb(input, userId, this.tenantId);

    const data = await execSafeQuery<ClientRow>(() =>
      this.supabase
        .schema('tenants')
        .from('clients')
        .insert(clientData)
        .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)')
        .single()
    );

    if (!data) {
      throw new Error('Failed to create client');
    }

    return mapClientFromDb(data);
  }

  /**
   * Update a client
   */
  async update(id: string, userId: string, input: Partial<ClientInput>): Promise<Client> {
    const updateData = mapClientUpdateToDb(input, userId);

    const data = await execSafeQuery<ClientRow>(() =>
      this.supabase
        .schema('tenants')
        .from('clients')
        .update(updateData)
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)')
        .single()
    );

    if (!data) {
      throw new Error('Failed to update client');
    }

    return mapClientFromDb(data);
  }

  /**
   * Delete a client
   */
  async delete(id: string): Promise<void> {
    // First check if this client is associated with any cases
    const { data: caseClients, error: checkError } = await this.supabase
      .schema('tenants')
      .from('matter_clients')
      .select('matter_id')
      .eq('client_id', id)
      .eq('tenant_id', this.tenantId);

    if (checkError) {
      console.error('Error checking client case associations:', checkError);
      throw new Error(`Failed to check client case associations: ${checkError.message}`);
    }

    // If client is associated with cases, don't allow deletion
    if (caseClients && caseClients.length > 0) {
      throw new Error('Cannot delete client: Client is associated with one or more cases');
    }

    // Proceed with deletion
    const { error } = await this.supabase
      .schema('tenants')
      .from('clients')
      .delete()
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error deleting client:', error);
      throw new Error(`Failed to delete client: ${error.message}`);
    }
  }

  /**
   * Get clients by IDs (batch fetch)
   */
  async getByIds(ids: string[]): Promise<Client[]> {
    if (!ids.length) {
      return [];
    }

    const { data, error } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)')
      .in('id', ids)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error fetching clients by IDs:', error);
      throw new Error(`Failed to fetch clients by IDs: ${error.message}`);
    }

    return mapClientsFromDb(data || []);
  }

  /**
   * Get client statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byStatus: Record<string, number>;
  }> {
    // Get total count
    const { count: total } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId);

    // Get active count
    const { count: active } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId)
      .eq('status', 'active');

    // Get inactive count
    const { count: inactive } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId)
      .eq('status', 'inactive');

    // Get count breakdown by status
    const { data: statusData, error: statusError } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('status')
      .eq('tenant_id', this.tenantId);

    if (statusError) {
      console.error('Error fetching client status breakdown:', statusError);
      throw new Error(`Failed to fetch client status breakdown: ${statusError.message}`);
    }

    // Count occurrences of each status
    const byStatus: Record<string, number> = {};
    statusData?.forEach(client => {
      const status = client.status || 'unknown';
      byStatus[status] = (byStatus[status] || 0) + 1;
    });

    return {
      total: total || 0,
      active: active || 0,
      inactive: inactive || 0,
      byStatus
    };
  }
}
